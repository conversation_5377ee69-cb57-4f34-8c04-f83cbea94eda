package dtos

// AuthenticationRequest represents the login request
type AuthenticationRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest represents the register request
type RegisterRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Name     string `json:"name" binding:"required"`
}

// AuthenticationResponse represents the login response
type AuthenticationResponse struct {
	Token string  `json:"token"`
	User  UserDTO `json:"user"`
}

// UserDTO represents the user data
type UserDTO struct {
	ID               string `json:"id"`
	Username         string `json:"username"`
	Email            string `json:"email"`
	Name             string `json:"name"`
	IsGuest          bool   `json:"is_guest"`
	GuestID          string `json:"guest_id,omitempty"`
	Plan             string `json:"plan"`
	TransactionLimit int    `json:"transaction_limit"`
	TransactionCount int    `json:"transaction_count"`
}

// CreateGuestUserRequest represents the guest user creation request
type CreateGuestUserRequest struct {
	DeviceID string `json:"device_id" binding:"required"`
}

// GuestToRegisteredRequest represents the guest to registered conversion request
type GuestToRegisteredRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Name     string `json:"name" binding:"required"`
}

// ChangePasswordRequest represents the change password request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"currentPassword" binding:"required"`
	NewPassword     string `json:"newPassword" binding:"required,min=6"`
}

// PlanUpgradeRequest represents the plan upgrade request
type PlanUpgradeRequest struct {
	Plan string `json:"plan" binding:"required,oneof=free premium"`
}

// TransactionLimitResponse represents the transaction limit status
type TransactionLimitResponse struct {
	CanCreateTransaction  bool `json:"can_create_transaction"`
	TransactionCount      int  `json:"transaction_count"`
	TransactionLimit      int  `json:"transaction_limit"`
	RemainingTransactions int  `json:"remaining_transactions"`
	RequiresUpgrade       bool `json:"requires_upgrade"`
}
