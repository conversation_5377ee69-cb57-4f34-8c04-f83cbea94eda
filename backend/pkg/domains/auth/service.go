package auth

import (
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/config"
	"github.com/nocytech/butce360/pkg/consts"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	"github.com/nocytech/butce360/pkg/fin_notebooklog"
	"github.com/nocytech/butce360/pkg/utils"
)

type Service interface {
	Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error)
	CreateUser(payload dtos.CreateUserReqDto) error
	Register(payload *dtos.RegisterRequest) (dtos.AuthenticationResponse, error)
	GetUserByID(userID string) (*dtos.UserDTO, error)
	Logout(token string, userID string) error
	InitializeUserDefaults(userID string) error
	GoogleOAuthLogin(email, googleID, name string) (dtos.AuthenticationResponse, error)
	AppleOAuthLogin(identityToken, userIdentifier, email, givenName, familyName string, isFirstTimeSignIn bool) (dtos.AuthenticationResponse, error)
	SoftDeleteUser(userID string) error

	// Guest user methods
	CreateGuestUser(deviceID string) (dtos.AuthenticationResponse, error)
	ConvertGuestToRegistered(guestID string, payload *dtos.GuestToRegisteredRequest) (dtos.AuthenticationResponse, error)
	GetTransactionLimitStatus(userID string) (*dtos.TransactionLimitResponse, error)
	UpdateTransactionCount(userID string) error
}

// CategoryService interface for dependency injection
type CategoryService interface {
	CreateCategory(userID string, req *dtos.CategoryRequest) (*dtos.CategoryResponse, error)
}

// AccountService interface for dependency injection
type AccountService interface {
	CreateAccount(userID string, req *dtos.AccountRequest) (*dtos.AccountResponse, error)
}

type service struct {
	repository      Repository
	categoryService CategoryService
	accountService  AccountService
}

func NewService(r Repository, cs CategoryService, as AccountService) Service {
	return &service{
		repository:      r,
		categoryService: cs,
		accountService:  as,
	}
}

func (s *service) CreateUser(payload dtos.CreateUserReqDto) error {
	return s.repository.CreateUser(payload)
}

func (s *service) Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	user, err := s.repository.GetUserByUserName(payload.Username)
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}
	if !utils.Compare(user.Password, payload.Password) {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: consts.LoginFailed,
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}

	fmt.Println("jwt secret:", config.ReadValue().App.JwtSecret)
	fmt.Println("jwt expire:", config.ReadValue().App.JwtExpire)

	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})

		return resp, errors.New(consts.LoginFailed)
	}

	resp.Token = token

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:               user.ID.String(),
		Username:         user.Username,
		Email:            user.Email,
		Name:             user.Name,
		IsGuest:          user.IsGuest,
		GuestID:          user.GuestID,
		Plan:             user.Plan,
		TransactionLimit: user.TransactionLimit,
		TransactionCount: user.TransactionCount,
	}
	resp.User = userDTO

	fmt.Println("test:", resp.Token)

	return resp, nil
}

func (s *service) Register(payload *dtos.RegisterRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	// Check if username already exists
	_, err := s.repository.GetUserByUserName(payload.Username)
	if err == nil {
		return resp, errors.New("username already exists")
	}

	// Check if email already exists
	_, err = s.repository.GetUserByEmail(payload.Email)
	if err == nil {
		return resp, errors.New("email already exists")
	}

	// Create user
	user := &entities.User{
		Username: payload.Username,
		Email:    payload.Email,
		Password: payload.Password,
		Name:     payload.Name,
		Status:   "active",
	}

	err = s.repository.Register(user)
	if err != nil {
		return resp, err
	}

	// Generate token
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		return resp, err
	}

	resp.Token = token

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:               user.ID.String(),
		Username:         user.Username,
		Email:            user.Email,
		Name:             user.Name,
		IsGuest:          user.IsGuest,
		GuestID:          user.GuestID,
		Plan:             user.Plan,
		TransactionLimit: user.TransactionLimit,
		TransactionCount: user.TransactionCount,
	}
	resp.User = userDTO

	// Initialize default categories and accounts for the new user
	if err := s.InitializeUserDefaults(user.ID.String()); err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "User Defaults Initialization Failed",
			Message: "Failed to initialize default categories and accounts for user: " + err.Error(),
			Entity:  "user",
			Type:    "warning",
		})
		// Don't fail the registration if default initialization fails
	}

	return resp, nil
}

func (s *service) GetUserByID(userID string) (*dtos.UserDTO, error) {
	user, err := s.repository.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	userDTO := &dtos.UserDTO{
		ID:               user.ID.String(),
		Username:         user.Username,
		Email:            user.Email,
		Name:             user.Name,
		IsGuest:          user.IsGuest,
		GuestID:          user.GuestID,
		Plan:             user.Plan,
		TransactionLimit: user.TransactionLimit,
		TransactionCount: user.TransactionCount,
	}

	return userDTO, nil
}

func (s *service) Logout(token string, userID string) error {
	// Parse the token to get the expiration time
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
	}
	claims, err := jwt.ParseToken(token)
	if err != nil {
		return err
	}

	// Add the token to the blacklist
	expiresAt := claims.ExpiresAt.Time
	return s.repository.BlacklistToken(token, userID, expiresAt)
}

// InitializeUserDefaults creates default categories and accounts for a new user
func (s *service) InitializeUserDefaults(userID string) error {
	// Create default expense categories (Turkish)
	expenseCategories := []dtos.CategoryRequest{
		{Name: "Market", Type: "expense", Icon: "🛒"},
		{Name: "Restoran", Type: "expense", Icon: "🍽️"},
		{Name: "Ulaşım", Type: "expense", Icon: "🚗"},
		{Name: "Kira", Type: "expense", Icon: "🏠"},
		{Name: "Faturalar", Type: "expense", Icon: "�"},
		{Name: "Eğlence", Type: "expense", Icon: "🎬"},
		{Name: "Sağlık", Type: "expense", Icon: "�"},
		{Name: "Alışveriş", Type: "expense", Icon: "🛍️"},
		{Name: "Yakıt", Type: "expense", Icon: "⛽"},
		{Name: "Telefon", Type: "expense", Icon: "�"},
		{Name: "İnternet", Type: "expense", Icon: "🌐"},
		{Name: "Giyim", Type: "expense", Icon: "👕"},
		{Name: "Eğitim", Type: "expense", Icon: "📚"},
		{Name: "Spor", Type: "expense", Icon: "�️"},
		{Name: "Güzellik", Type: "expense", Icon: "💄"},
		{Name: "Hediye", Type: "expense", Icon: "🎁"},
		{Name: "Seyahat", Type: "expense", Icon: "✈️"},
		{Name: "Sigorta", Type: "expense", Icon: "🛡️"},
		{Name: "Vergi", Type: "expense", Icon: "📋"},
		{Name: "Diğer", Type: "expense", Icon: "�"},
	}

	// Create default income categories (Turkish)
	incomeCategories := []dtos.CategoryRequest{
		{Name: "Maaş", Type: "income", Icon: "💰"},
		{Name: "Freelance", Type: "income", Icon: "💻"},
		{Name: "Yatırım", Type: "income", Icon: "📈"},
		{Name: "Hediye", Type: "income", Icon: "🎁"},
		{Name: "Bonus", Type: "income", Icon: "🎯"},
		{Name: "Kira Geliri", Type: "income", Icon: "🏠"},
		{Name: "Faiz", Type: "income", Icon: "🏦"},
		{Name: "Satış", Type: "income", Icon: "�"},
		{Name: "Diğer", Type: "income", Icon: "📝"},
	}

	// Create expense categories
	for _, category := range expenseCategories {
		_, err := s.categoryService.CreateCategory(userID, &category)
		if err != nil {
			return fmt.Errorf("failed to create expense category %s: %w", category.Name, err)
		}
	}

	// Create income categories
	for _, category := range incomeCategories {
		_, err := s.categoryService.CreateCategory(userID, &category)
		if err != nil {
			return fmt.Errorf("failed to create income category %s: %w", category.Name, err)
		}
	}

	// Create default accounts (Turkish)
	defaultAccounts := []dtos.AccountRequest{
		{Name: "Nakit", Type: "cash", Balance: 0, Currency: "TRY"},
		{Name: "Banka Hesabı", Type: "bank", Balance: 0, Currency: "TRY"},
		{Name: "Kredi Kartı", Type: "credit", Balance: 0, Currency: "TRY"},
		{Name: "Vadesiz Hesap", Type: "bank", Balance: 0, Currency: "TRY"},
		{Name: "Vadeli Hesap", Type: "savings", Balance: 0, Currency: "TRY"},
	}

	// Create accounts
	for _, account := range defaultAccounts {
		_, err := s.accountService.CreateAccount(userID, &account)
		if err != nil {
			return fmt.Errorf("failed to create account %s: %w", account.Name, err)
		}
	}

	return nil
}

// GoogleOAuthLogin handles Google OAuth login/registration
func (s *service) GoogleOAuthLogin(email, googleID, name string) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse

	// First, try to find user by Google ID
	user, err := s.repository.GetUserByGoogleID(googleID)
	if err != nil && err.Error() != consts.UserNotFound {
		return resp, err
	}

	// If user not found by Google ID, try to find by email
	if user == nil {
		user, err = s.repository.GetUserByEmail(email)
		if err != nil && err.Error() != consts.UserNotFound {
			return resp, err
		}

		// If user exists with this email but no Google ID, update their Google ID
		if user != nil && (user.GoogleID == nil || *user.GoogleID == "") {
			user.GoogleID = &googleID
			if err := s.repository.UpdateUser(user); err != nil {
				return resp, fmt.Errorf("failed to update user with Google ID: %w", err)
			}
		}
	}

	// If user still not found, create a new user
	if user == nil {
		newUser := &entities.User{
			Base: entities.Base{
				ID: uuid.New(),
			},
			Username: email, // Use email as username for OAuth users
			Email:    email,
			Name:     name,
			GoogleID: &googleID,
			Status:   "active",
		}

		if err := s.repository.CreateOAuthUser(newUser); err != nil {
			return resp, fmt.Errorf("failed to create OAuth user: %w", err)
		}

		// Initialize default categories and accounts for the new user
		if err := s.InitializeUserDefaults(newUser.ID.String()); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "User Defaults Initialization Failed",
				Message: "Failed to initialize default categories and accounts for OAuth user: " + err.Error(),
				Entity:  "user",
				Type:    "warning",
			})
			// Don't fail the login if default initialization fails
		}

		user = newUser
	}

	// Generate JWT token
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		return resp, fmt.Errorf("failed to generate JWT token: %w", err)
	}

	resp.Token = token

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:               user.ID.String(),
		Username:         user.Username,
		Email:            user.Email,
		Name:             user.Name,
		IsGuest:          user.IsGuest,
		GuestID:          user.GuestID,
		Plan:             user.Plan,
		TransactionLimit: user.TransactionLimit,
		TransactionCount: user.TransactionCount,
	}
	resp.User = userDTO

	return resp, nil
}

// CreateGuestUser creates a new guest user and returns authentication response
func (s *service) CreateGuestUser(deviceID string) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse

	// Create guest user
	user, err := s.repository.CreateGuestUser(deviceID)
	if err != nil {
		return resp, err
	}

	// Generate JWT token
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Guest Token Generation Failed",
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New("failed to generate token")
	}

	resp.Token = token

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:               user.ID.String(),
		Username:         user.Username,
		Email:            user.Email,
		Name:             user.Name,
		IsGuest:          user.IsGuest,
		GuestID:          user.GuestID,
		Plan:             user.Plan,
		TransactionLimit: user.TransactionLimit,
		TransactionCount: user.TransactionCount,
	}
	resp.User = userDTO

	// Initialize default categories and accounts for the guest user
	if err := s.InitializeUserDefaults(user.ID.String()); err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Guest User Defaults Initialization Failed",
			Message: "Failed to initialize default categories and accounts for guest user: " + err.Error(),
			Entity:  "user",
			Type:    "warning",
			UserID:  user.ID,
		})
		// Don't fail the guest creation if default initialization fails
	}

	return resp, nil
}

// ConvertGuestToRegistered converts a guest user to a registered user
func (s *service) ConvertGuestToRegistered(guestID string, payload *dtos.GuestToRegisteredRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse

	// Create user entity from payload
	userData := &entities.User{
		Username: payload.Username,
		Email:    payload.Email,
		Password: payload.Password,
		Name:     payload.Name,
	}

	// Convert guest to registered
	err := s.repository.ConvertGuestToRegistered(guestID, userData)
	if err != nil {
		return resp, err
	}

	// Get the updated user
	user, err := s.repository.GetUserByEmail(payload.Email)
	if err != nil {
		return resp, err
	}

	// Generate new JWT token
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Converted User Token Generation Failed",
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New("failed to generate token")
	}

	resp.Token = token

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:               user.ID.String(),
		Username:         user.Username,
		Email:            user.Email,
		Name:             user.Name,
		IsGuest:          user.IsGuest,
		GuestID:          user.GuestID,
		Plan:             user.Plan,
		TransactionLimit: user.TransactionLimit,
		TransactionCount: user.TransactionCount,
	}
	resp.User = userDTO

	return resp, nil
}

// GetTransactionLimitStatus returns the transaction limit status for a user
func (s *service) GetTransactionLimitStatus(userID string) (*dtos.TransactionLimitResponse, error) {
	return s.repository.GetTransactionLimitStatus(userID)
}

// UpdateTransactionCount increments the transaction count for a user
func (s *service) UpdateTransactionCount(userID string) error {
	return s.repository.UpdateTransactionCount(userID)
}

// Apple JWKS response structures
type AppleKeys struct {
	Keys []AppleKey `json:"keys"`
}

type AppleKey struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
	Crv string `json:"crv"`
	X   string `json:"x"`
	Y   string `json:"y"`
}

// fetchApplePublicKeys fetches Apple's public keys for token verification
func (s *service) fetchApplePublicKeys() (*AppleKeys, error) {
	resp, err := http.Get("https://appleid.apple.com/auth/keys")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var keys AppleKeys
	if err := json.Unmarshal(body, &keys); err != nil {
		return nil, err
	}
	return &keys, nil
}

// decodeBase64URL decodes base64 URL encoded string
func (s *service) decodeBase64URL(str string) ([]byte, error) {
	return base64.RawURLEncoding.DecodeString(str)
}

// verifyAppleIdentityToken verifies Apple identity token
func (s *service) verifyAppleIdentityToken(identityToken string) (*jwt.Token, error) {
	// 1. Fetch Apple public keys
	keys, err := s.fetchApplePublicKeys()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch Apple public keys: %w", err)
	}

	// 2. Parse token and get kid from header
	token, err := jwt.Parse(identityToken, func(t *jwt.Token) (interface{}, error) {
		// Check kid
		kid, ok := t.Header["kid"].(string)
		if !ok {
			return nil, fmt.Errorf("kid not found in token header")
		}

		// Find matching Apple key
		var appleKey *AppleKey
		for _, k := range keys.Keys {
			if k.Kid == kid {
				appleKey = &k
				break
			}
		}
		if appleKey == nil {
			return nil, fmt.Errorf("no matching Apple public key found")
		}

		// Handle different key types
		switch appleKey.Kty {
		case "EC":
			// ECDSA key (ES256)
			xBytes, err := s.decodeBase64URL(appleKey.X)
			if err != nil {
				return nil, fmt.Errorf("failed to decode X coordinate: %w", err)
			}
			yBytes, err := s.decodeBase64URL(appleKey.Y)
			if err != nil {
				return nil, fmt.Errorf("failed to decode Y coordinate: %w", err)
			}

			x := new(big.Int).SetBytes(xBytes)
			y := new(big.Int).SetBytes(yBytes)

			pubKey := &ecdsa.PublicKey{
				Curve: elliptic.P256(),
				X:     x,
				Y:     y,
			}

			return pubKey, nil

		case "RSA":
			// RSA key (RS256)
			nBytes, err := s.decodeBase64URL(appleKey.N)
			if err != nil {
				return nil, fmt.Errorf("failed to decode RSA modulus: %w", err)
			}
			eBytes, err := s.decodeBase64URL(appleKey.E)
			if err != nil {
				return nil, fmt.Errorf("failed to decode RSA exponent: %w", err)
			}

			n := new(big.Int).SetBytes(nBytes)
			e := new(big.Int).SetBytes(eBytes)

			pubKey := &rsa.PublicKey{
				N: n,
				E: int(e.Int64()),
			}

			return pubKey, nil

		default:
			return nil, fmt.Errorf("unsupported key type: %s", appleKey.Kty)
		}
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse Apple identity token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid Apple identity token")
	}

	return token, nil
}

// AppleOAuthLogin handles Apple OAuth login/registration
func (s *service) AppleOAuthLogin(identityToken, userIdentifier, email, givenName, familyName string, isFirstTimeSignIn bool) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	var appleUserID string

	// Check if this is a mock token for development/testing
	if strings.HasPrefix(identityToken, "mock_identity_token_") {
		// Use userIdentifier as Apple user ID for mock tokens
		appleUserID = userIdentifier
		fmt.Printf("[AppleOAuth] Using mock token for development: %s\n", userIdentifier)
	} else {
		// Verify real Apple identity token
		token, err := s.verifyAppleIdentityToken(identityToken)
		if err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "Apple Token Verification Failed",
				Message: "Apple identity token verification failed: " + err.Error(),
				Type:    "error",
				Entity:  "auth",
			})
			return resp, fmt.Errorf("invalid Apple identity token: %w", err)
		}

		// Extract claims from token
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			return resp, fmt.Errorf("failed to parse Apple token claims")
		}
		fmt.Println("Claims from Apple token: %v", claims)

		// Get Apple user ID from token
		var ok2 bool
		appleUserID, ok2 = claims["sub"].(string)
		if !ok2 {
			return resp, fmt.Errorf("Apple user ID not found in token")
		}

	}

	// First, try to find user by Apple ID
	user, err := s.repository.GetUserByAppleID(appleUserID)
	if err != nil && err.Error() != consts.UserNotFound {
		return resp, err
	}

	// If user not found by Apple ID, try to find by email (if provided)
	if user == nil && email != "" {
		user, err = s.repository.GetUserByEmail(email)
		if err != nil && err.Error() != consts.UserNotFound {
			return resp, err
		}

		// If user exists with this email but no Apple ID, update their Apple ID
		if user != nil && (user.AppleID == nil || *user.AppleID == "") {
			user.AppleID = &appleUserID
			if err := s.repository.UpdateUser(user); err != nil {
				return resp, fmt.Errorf("failed to update user with Apple ID: %w", err)
			}
		}
	}

	// If user still not found, create a new user
	if user == nil {
		name := strings.TrimSpace(givenName + " " + familyName)
		if name == "" {
			name = "Apple User"
		}

		// Use email if provided, otherwise create a placeholder
		userEmail := email
		if userEmail == "" {
			userEmail = fmt.Sprintf("<EMAIL>", appleUserID)
		}

		newUser := &entities.User{
			Base: entities.Base{
				ID: uuid.New(),
			},
			Username: userEmail, // Use email as username for OAuth users
			Email:    userEmail,
			Name:     name,
			AppleID:  &appleUserID,
			Status:   "active",
			Plan:     "free",
		}

		if err := s.repository.CreateOAuthUser(newUser); err != nil {
			return resp, fmt.Errorf("failed to create Apple OAuth user: %w", err)
		}

		// Initialize default categories and accounts for the new user
		if err := s.InitializeUserDefaults(newUser.ID.String()); err != nil {
			fin_notebooklog.CreateLog(&entities.Log{
				Title:   "User Defaults Initialization Failed",
				Message: "Failed to initialize default categories and accounts for Apple OAuth user: " + err.Error(),
				Entity:  "user",
				Type:    "warning",
			})
			// Don't fail the login if default initialization fails
		}

		user = newUser
	}

	// Generate JWT token
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	jwtToken, err := jwt.GenerateJWTWithUserInfo(user.Username, user.ID.String(), user.IsGuest, user.GuestID, user.Plan)
	if err != nil {
		return resp, fmt.Errorf("failed to generate JWT token: %w", err)
	}

	resp.Token = jwtToken

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:               user.ID.String(),
		Username:         user.Username,
		Email:            user.Email,
		Name:             user.Name,
		IsGuest:          user.IsGuest,
		GuestID:          user.GuestID,
		Plan:             user.Plan,
		TransactionLimit: user.TransactionLimit,
		TransactionCount: user.TransactionCount,
	}
	resp.User = userDTO

	fin_notebooklog.CreateLog(&entities.Log{
		Title:   "Apple OAuth Login Success",
		Message: "User successfully logged in with Apple ID: " + appleUserID,
		Entity:  "auth",
		Type:    "info",
		UserID:  user.ID,
	})

	return resp, nil
}

func (s *service) SoftDeleteUser(userID string) error {
	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	// Soft delete user by updating status to "deleted"
	return s.repository.SoftDeleteUser(userUUID)
}
