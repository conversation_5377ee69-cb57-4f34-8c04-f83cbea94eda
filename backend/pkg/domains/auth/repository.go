package auth

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/consts"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	"github.com/nocytech/butce360/pkg/fin_notebooklog"
	"github.com/nocytech/butce360/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	CreateUser(dtos dtos.CreateUserReqDto) error
	GetUserByUserName(username string) (entities.User, error)
	Register(user *entities.User) error
	GetUserByID(userID string) (*entities.User, error)
	GetUserByEmail(email string) (*entities.User, error)
	GetUserByGoogleID(googleID string) (*entities.User, error)
	GetUserByAppleID(appleID string) (*entities.User, error)
	CreateOAuthUser(user *entities.User) error
	UpdateUser(user *entities.User) error
	UpdateUserPassword(userID uuid.UUID, hashedPassword string) error
	SoftDeleteUser(userID uuid.UUID) error

	// Guest user methods
	CreateGuestUser(deviceID string) (*entities.User, error)
	GetUserByGuestID(guestID string) (*entities.User, error)
	ConvertGuestToRegistered(guestID string, userData *entities.User) error
	UpdateTransactionCount(userID string) error
	GetTransactionLimitStatus(userID string) (*dtos.TransactionLimitResponse, error)

	// Token blacklist methods
	BlacklistToken(token string, userID string, expiresAt time.Time) error
	IsTokenBlacklisted(token string) bool
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Register(user *entities.User) error {
	// Hash the password
	hashedPassword := utils.Bcrypt(user.Password)
	user.Password = hashedPassword
	user.Status = "active"

	// Create the user
	if err := r.db.Create(user).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Register Failed",
			Message: "Register Failed, create user err:" + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return err
	}
	return nil
}

func (r *repository) GetUserByID(userID string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetUserByEmail(email string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

func (r *repository) CreateUser(dtos dtos.CreateUserReqDto) error {
	var user = entities.User{
		Username: dtos.Username,
		Password: utils.Bcrypt(dtos.Password),
		Name:     dtos.Name,
	}
	err := r.db.Create(&user).Error
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Create User Error",
			Message: "Create User Error: " + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return errors.New(consts.CreateUserFailed)
	}
	return nil

}

func (r *repository) GetUserByUserName(username string) (entities.User, error) {
	var user entities.User
	err := r.db.Where(consts.UserName+" = ?", username).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, err
}

// BlacklistToken adds a token to the blacklist
func (r *repository) BlacklistToken(token string, userID string, expiresAt time.Time) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	tokenBlacklist := &entities.TokenBlacklist{
		Token:     token,
		UserID:    userUUID,
		ExpiresAt: expiresAt,
	}

	if err := r.db.Create(tokenBlacklist).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Blacklist Token Failed",
			Message: "Blacklist Token Failed, create token blacklist err:" + err.Error(),
			Entity:  "token_blacklist",
			Type:    "error",
			UserID:  userUUID,
		})
		return err
	}
	return nil
}

// IsTokenBlacklisted checks if a token is blacklisted
func (r *repository) IsTokenBlacklisted(token string) bool {
	var count int64
	r.db.Model(&entities.TokenBlacklist{}).Where("token = ?", token).Count(&count)
	return count > 0
}

// GetUserByGoogleID retrieves a user by their Google ID
func (r *repository) GetUserByGoogleID(googleID string) (*entities.User, error) {
	var user entities.User
	err := r.db.Where("google_id = ?", googleID).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

// GetUserByAppleID retrieves a user by their Apple ID
func (r *repository) GetUserByAppleID(appleID string) (*entities.User, error) {
	var user entities.User
	err := r.db.Where("apple_id = ?", appleID).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

// CreateOAuthUser creates a new user from OAuth provider
func (r *repository) CreateOAuthUser(user *entities.User) error {
	user.Status = "active"

	// Create the user
	if err := r.db.Create(user).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "OAuth User Creation Failed",
			Message: "OAuth User Creation Failed, create user err:" + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return err
	}
	return nil
}

// UpdateUser updates an existing user
func (r *repository) UpdateUser(user *entities.User) error {
	if err := r.db.Save(user).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "User Update Failed",
			Message: "User Update Failed, save user err:" + err.Error(),
			Entity:  "user",
			Type:    "error",
			UserID:  user.ID,
		})
		return err
	}
	return nil
}

// CreateGuestUser creates a new guest user
func (r *repository) CreateGuestUser(deviceID string) (*entities.User, error) {
	guestID := uuid.New().String()

	user := &entities.User{
		Username:         "guest_" + guestID[:8],
		Name:             "Guest User",
		Status:           "active",
		IsGuest:          true,
		GuestID:          guestID,
		Plan:             "guest",
		TransactionLimit: 5, // Default guest limit
		TransactionCount: 0,
	}

	if err := r.db.Create(user).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Create Guest User Failed",
			Message: "Create Guest User Failed, create user err:" + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return nil, err
	}

	fin_notebooklog.CreateLog(&entities.Log{
		Title:   "Guest User Created",
		Message: "Guest user created successfully with ID: " + guestID,
		Entity:  "user",
		Type:    "info",
		UserID:  user.ID,
	})

	return user, nil
}

// GetUserByGuestID retrieves a user by guest ID
func (r *repository) GetUserByGuestID(guestID string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("guest_id = ? AND is_guest = ?", guestID, true).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

// ConvertGuestToRegistered converts a guest user to registered user
func (r *repository) ConvertGuestToRegistered(guestID string, userData *entities.User) error {
	// Start transaction
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get guest user
	var guestUser entities.User
	if err := tx.Where("guest_id = ? AND is_guest = ?", guestID, true).First(&guestUser).Error; err != nil {
		tx.Rollback()
		return errors.New("guest user not found")
	}

	// Check if email already exists
	var existingUser entities.User
	if err := tx.Where("email = ? AND is_guest = ?", userData.Email, false).First(&existingUser).Error; err == nil {
		tx.Rollback()
		return errors.New("email already exists")
	}

	// Check if username already exists
	if err := tx.Where("username = ? AND is_guest = ?", userData.Username, false).First(&existingUser).Error; err == nil {
		tx.Rollback()
		return errors.New("username already exists")
	}

	// Update guest user to registered user
	guestUser.Username = userData.Username
	guestUser.Email = userData.Email
	guestUser.Password = utils.Bcrypt(userData.Password)
	guestUser.Name = userData.Name
	guestUser.ConvertToRegistered()

	if err := tx.Save(&guestUser).Error; err != nil {
		tx.Rollback()
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Guest to Registered Conversion Failed",
			Message: "Failed to convert guest to registered user: " + err.Error(),
			Entity:  "user",
			Type:    "error",
			UserID:  guestUser.ID,
		})
		return err
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return err
	}

	fin_notebooklog.CreateLog(&entities.Log{
		Title:   "Guest User Converted",
		Message: "Guest user successfully converted to registered user: " + userData.Username,
		Entity:  "user",
		Type:    "info",
		UserID:  guestUser.ID,
	})

	return nil
}

// UpdateTransactionCount increments the transaction count for a user
func (r *repository) UpdateTransactionCount(userID string) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	if err := r.db.Model(&entities.User{}).Where("id = ?", userUUID).UpdateColumn("transaction_count", gorm.Expr("transaction_count + ?", 1)).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Transaction Count Update Failed",
			Message: "Failed to update transaction count: " + err.Error(),
			Entity:  "user",
			Type:    "error",
			UserID:  userUUID,
		})
		return err
	}
	return nil
}

// GetTransactionLimitStatus returns the transaction limit status for a user
func (r *repository) GetTransactionLimitStatus(userID string) (*dtos.TransactionLimitResponse, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	var user entities.User
	if err := r.db.Where("id = ?", userUUID).First(&user).Error; err != nil {
		return nil, err
	}

	response := &dtos.TransactionLimitResponse{
		CanCreateTransaction:  user.CanCreateTransaction(),
		TransactionCount:      user.TransactionCount,
		TransactionLimit:      user.TransactionLimit,
		RemainingTransactions: user.GetRemainingTransactions(),
		RequiresUpgrade:       user.IsGuest && !user.CanCreateTransaction(),
	}

	return response, nil
}

// SoftDeleteUser soft deletes a user by updating their status to "deleted"
func (r *repository) SoftDeleteUser(userID uuid.UUID) error {
	if err := r.db.Model(&entities.User{}).Where("id = ?", userID).Delete(&entities.User{}).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "User Soft Delete Failed",
			Message: "Failed to soft delete user: " + err.Error(),
			Entity:  "user",
			Type:    "error",
			UserID:  userID,
		})
		return err
	}

	fin_notebooklog.CreateLog(&entities.Log{
		Title:   "User Soft Deleted",
		Message: "User account successfully soft deleted",
		Entity:  "user",
		Type:    "info",
		UserID:  userID,
	})

	return nil
}

// UpdateUserPassword updates the user's password
func (r *repository) UpdateUserPassword(userID uuid.UUID, hashedPassword string) error {
	if err := r.db.Model(&entities.User{}).Where("id = ?", userID).Update("password", hashedPassword).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Password Update Failed",
			Message: "Failed to update user password: " + err.Error(),
			Entity:  "user",
			Type:    "error",
			UserID:  userID,
		})
		return err
	}

	fin_notebooklog.CreateLog(&entities.Log{
		Title:   "Password Updated",
		Message: "User password updated successfully",
		Entity:  "user",
		Type:    "info",
		UserID:  userID,
	})

	return nil
}
