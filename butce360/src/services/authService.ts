import { apiClient } from './api';
import { storage, STORAGE_KEYS } from './storage';
import {
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  GuestToRegisteredRequest,
  TransactionLimitResponse,
  // ApiError // Not used
} from '../types/models';

export class AuthService {
  private static instance: AuthService;

  private constructor() {}

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      console.log('[Auth] Attempting login for user:', credentials.username);

      const response = await apiClient.post<AuthResponse>('/login', credentials);

      // Store token and user data
      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await storage.setObject(STORAGE_KEYS.AUTH_USER, response.data.user);

      console.log('[Auth] Login successful for user:', response.data.user.username);
      return response.data;
    } catch (error) {
      console.error('[Auth] Login error:', error);
      throw error;
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    try {
      console.log('[Auth] Attempting registration for user:', userData.username);

      const response = await apiClient.post<AuthResponse>('/register', userData);

      // Store token and user data
      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await storage.setObject(STORAGE_KEYS.AUTH_USER, response.data.user);

      console.log('[Auth] Registration successful for user:', response.data.user.username);
      return response.data;
    } catch (error) {
      console.error('[Auth] Registration error:', error);
      throw error;
    }
  }

  // Google Sign-In (Mobile)
  async googleSignIn(googleUser: any): Promise<AuthResponse> {
    try {
      console.log('[Auth] Attempting Google sign-in for user:', googleUser.email);

      const response = await apiClient.post<AuthResponse>('/google/mobile', {
        id_token: googleUser.idToken,
        access_token: googleUser.accessToken,
      });

      // Store token and user data
      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await storage.setObject(STORAGE_KEYS.AUTH_USER, response.data.user);

      console.log('[Auth] Google sign-in successful for user:', response.data.user.email);
      return response.data;
    } catch (error) {
      console.error('[Auth] Google sign-in error:', error);
      throw error;
    }
  }

  // Apple Sign-In (Mobile)
  async appleSignIn(appleData: {
    identityToken: string;
    authorizationCode?: string;
    userIdentifier: string;
    email?: string;
    fullName?: { givenName?: string; familyName?: string };
  }): Promise<AuthResponse> {
    try {
      console.log('[Auth] Attempting Apple sign-in for user:', appleData.email || appleData.userIdentifier);

      const response = await apiClient.post<AuthResponse>('/apple/mobile', {
        identity_token: appleData.identityToken,
        authorization_code: appleData.authorizationCode,
        user_identifier: appleData.userIdentifier,
        email: appleData.email,
        full_name: {
          given_name: appleData.fullName?.givenName || '',
          family_name: appleData.fullName?.familyName || '',
        },
      });

      // Store token and user data
      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await storage.setObject(STORAGE_KEYS.AUTH_USER, response.data.user);

      console.log('[Auth] Apple sign-in successful for user:', response.data.user.email);
      return response.data;
    } catch (error) {
      console.error('[Auth] Apple sign-in error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      console.log('[Auth] Logging out user');

      // Try to notify server about logout
      try {
        const token = await this.getStoredToken();
        if (token) {
          await apiClient.post('/logout', {}, {
            'Authorization': `Bearer ${token}`
          });
        }
      } catch (error) {
        // Ignore server errors during logout
        console.log('[Auth] Server logout error (ignored):', error);
      }

      // Clear local storage
      await storage.multiRemove([STORAGE_KEYS.AUTH_TOKEN, STORAGE_KEYS.AUTH_USER]);
      console.log('[Auth] Logout successful');
    } catch (error) {
      console.error('[Auth] Logout error:', error);
      throw error;
    }
  }

  async getStoredToken(): Promise<string | null> {
    try {
      const token = await storage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      if (token) {
        console.log('[Auth] Token found, notifying AuthContext...');
        this.notifyTokenFound(token);
      }
      return token;
    } catch (error) {
      console.error('[Auth] Error getting stored token:', error);
      return null;
    }
  }

  // AuthContext'e token bulunduğunu bildirmek için callback
  private authStateCallback?: (token: string) => void;

  setAuthStateCallback(callback: (token: string) => void) {
    this.authStateCallback = callback;
  }

  private notifyTokenFound(token: string) {
    if (this.authStateCallback) {
      this.authStateCallback(token);
    }
  }

  async getStoredUser(): Promise<User | null> {
    try {
      return await storage.getObject<User>(STORAGE_KEYS.AUTH_USER);
    } catch (error) {
      console.error('[Auth] Error getting stored user:', error);
      return null;
    }
  }

  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await this.getStoredToken();
      const user = await this.getStoredUser();
      return !!(token && user);
    } catch (error) {
      console.error('[Auth] Error checking authentication:', error);
      return false;
    }
  }

  async refreshToken(): Promise<string | null> {
    try {
      const currentToken = await this.getStoredToken();
      if (!currentToken) {
        throw new Error('No token available for refresh');
      }

      const response = await apiClient.post<{ token: string }>('/refresh', {}, {
        'Authorization': `Bearer ${currentToken}`
      });

      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      console.log('[Auth] Token refreshed successfully');
      return response.data.token;
    } catch (error) {
      console.error('[Auth] Token refresh error:', error);
      // Clear invalid token
      await storage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      throw error;
    }
  }

  async validateToken(): Promise<boolean> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        return false;
      }

      const response = await apiClient.get('/validate', {
        'Authorization': `Bearer ${token}`
      });

      return response.success;
    } catch (error) {
      console.error('[Auth] Token validation error:', error);
      return false;
    }
  }

  async updateProfile(userData: Partial<User>): Promise<User> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await apiClient.put<User>('/profile', userData, {
        'Authorization': `Bearer ${token}`
      });

      // Update stored user data
      await storage.setObject(STORAGE_KEYS.AUTH_USER, response.data);

      console.log('[Auth] Profile updated successfully');
      return response.data;
    } catch (error) {
      console.error('[Auth] Profile update error:', error);
      throw error;
    }
  }

  async deleteAccount(): Promise<void> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      await apiClient.delete('/delete-account', {
        'Authorization': `Bearer ${token}`
      });

      // Clear local storage after successful deletion
      await storage.multiRemove([STORAGE_KEYS.AUTH_TOKEN, STORAGE_KEYS.AUTH_USER]);
      console.log('[Auth] Account deleted successfully');
    } catch (error) {
      console.error('[Auth] Account deletion error:', error);
      throw error;
    }
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const token = await this.getStoredToken();
      if (!token) {
        throw new Error('No authentication token available');
      }

      await apiClient.post('/change-password', {
        currentPassword,
        newPassword
      }, {
        'Authorization': `Bearer ${token}`
      });

      console.log('[Auth] Password changed successfully');
    } catch (error) {
      console.error('[Auth] Password change error:', error);
      throw error;
    }
  }

  // Demo login for development and guest mode
  async demoLogin(): Promise<AuthResponse> {
    try {
      console.log('[Auth] Attempting demo login');

      const response = await apiClient.post<any>('/login', {
        username: 'demo',
        password: 'demo123'
      });

      // Backend returns {data: {token, user}, status}
      const authData = response.data.data || response.data;

      // Store token and user data
      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, authData.token);
      await storage.setObject(STORAGE_KEYS.AUTH_USER, authData.user);

      console.log('[Auth] Demo login successful for user:', authData.user.username);
      return authData;
    } catch (error) {
      console.error('[Auth] Demo login error:', error);
      throw error;
    }
  }

  // Guest mode management
  async enableGuestMode(): Promise<void> {
    try {
      // Clear any existing auth data
      await storage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      await storage.removeItem(STORAGE_KEYS.AUTH_USER);

      // Set guest mode
      await storage.setItem(STORAGE_KEYS.GUEST_MODE, 'true');
      console.log('[Auth] Guest mode enabled - auth data cleared');
    } catch (error) {
      console.error('[Auth] Error enabling guest mode:', error);
      throw error;
    }
  }

  async disableGuestMode(): Promise<void> {
    try {
      await storage.removeItem(STORAGE_KEYS.GUEST_MODE);
      console.log('[Auth] Guest mode disabled');
    } catch (error) {
      console.error('[Auth] Error disabling guest mode:', error);
      throw error;
    }
  }

  async isGuestMode(): Promise<boolean> {
    try {
      const guestMode = await storage.getItem(STORAGE_KEYS.GUEST_MODE);
      return guestMode === 'true';
    } catch (error) {
      console.error('[Auth] Error checking guest mode:', error);
      return false;
    }
  }

  // Clear all stored auth data
  async clearStorage(): Promise<void> {
    try {
      await storage.multiRemove([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.AUTH_USER,
        STORAGE_KEYS.GUEST_MODE
      ]);
      console.log('[Auth] Auth storage cleared');
    } catch (error) {
      console.error('[Auth] Error clearing auth storage:', error);
    }
  }

  // Guest User Methods
  async createGuestUser(deviceId: string): Promise<AuthResponse> {
    try {
      console.log('[Auth] Creating guest user for device:', deviceId);

      const response = await apiClient.post<AuthResponse>('/guest', { deviceId });

      // Store token and user data
      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await storage.setObject(STORAGE_KEYS.AUTH_USER, response.data.user);

      console.log('[Auth] Guest user created successfully');
      return response.data;
    } catch (error) {
      console.error('[Auth] Guest user creation error:', error);
      throw error;
    }
  }

  async convertGuestToRegistered(userData: GuestToRegisteredRequest): Promise<AuthResponse> {
    try {
      console.log('[Auth] Converting guest to registered user:', userData.username);

      const response = await apiClient.post<AuthResponse>('/guest/upgrade', userData);

      // Store new token and user data
      await storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);
      await storage.setObject(STORAGE_KEYS.AUTH_USER, response.data.user);

      console.log('[Auth] Guest conversion successful for user:', response.data.user.username);
      return response.data;
    } catch (error) {
      console.error('[Auth] Guest conversion error:', error);
      throw error;
    }
  }

  async getTransactionLimitStatus(): Promise<TransactionLimitResponse> {
    try {
      console.log('[Auth] Getting transaction limit status');

      const response = await apiClient.get<TransactionLimitResponse>('/limit-status');

      console.log('[Auth] Transaction limit status retrieved');
      return response.data;
    } catch (error) {
      console.error('[Auth] Transaction limit status error:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();